"""
Stage 10: Script Template Manager for GretahAI ScriptWeaver

This module provides the core business logic for Stage 10 (Script Template Manager).
It focuses on StateManager integration, template loading, AI orchestration, and script generation
while delegating all UI rendering to the ui_components.stage10_components module.

Core Responsibilities:
- StateManager integration and session state management
- Template loading and validation workflows
- AI gap analysis and script generation orchestration
- Integration with Google AI API through core/ai.py
- Script execution coordination with proper error handling
- Comprehensive logging controlled by SCRIPTWEAVER_DEBUG

The refactored architecture follows established GretahAI patterns:
- UI logic extracted to ui_components.stage10_components
- Core business logic focused on StateManager and orchestration
- Professional enterprise styling maintained through UI components
- Independent API key management (hidden from UI)
- Centralized error handling and logging

Functions:
    stage10_script_playground(state): Main Stage 10 orchestration function
"""

import os
import logging
import streamlit as st
import tempfile
import subprocess
import shutil
from datetime import datetime
from pathlib import Path

# Import core dependencies
from state_manager import StateStage
from core.ai import generate_llm_response
from core.ai_helpers import clean_llm_response
from debug_utils import debug

# Import helper functions
from core.template_helpers import (
    get_optimized_scripts_for_templates,
    format_template_script_display,
    get_available_test_cases,
    format_test_case_display,
    create_template_generation_filename,
    extract_template_structure_info
)

from core.template_prompt_builder import (
    generate_template_based_script_prompt,
    enhance_template_prompt_with_context
)

# Import UI components
from ui_components.stage10_components import (
    render_empty_playground_message,
    render_no_test_cases_message,
    render_template_selection_interface,
    render_test_case_selection_interface,
    render_script_generation_controls,
    render_script_execution_section_header,
    render_script_info_card,
    render_execution_controls_header,
    render_execution_options_card,
    render_verbose_mode_checkbox,
    render_execution_status_indicator,
    render_execution_action_buttons,
    render_execution_results_header,
    render_execution_results_summary,
    render_execution_metrics_header,
    render_junit_metrics_grid,
    render_execution_output_section,
    render_stage10_footer,
    render_workflow_navigation,
    render_generation_success_display
)

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage10")


def stage10_script_playground(state):
    """
    Stage 10: Script Template Manager.

    This function orchestrates the Stage 10 workflow by coordinating between UI components
    and core business logic. It focuses on StateManager integration, template loading,
    AI orchestration, and script generation while delegating UI rendering to components.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>🎮 Script Playground</h2>", unsafe_allow_html=True)
    st.markdown("*Experiment with script generation using optimized scripts as templates*")

    debug("Stage 10: Script Playground accessed")

    # Initialize script storage if needed
    if not hasattr(state, '_script_storage') or state._script_storage is None:
        state._init_script_storage()

    # Load optimized scripts for templates and available test cases
    optimized_scripts = get_optimized_scripts_for_templates(state._script_storage)
    available_test_cases = get_available_test_cases(state)

    # Handle empty playground state
    if not optimized_scripts:
        col1, col2, col3 = render_empty_playground_message()
        
        with col1:
            if st.button("📁 Start New Project", use_container_width=True, type="primary"):
                state.advance_to(StateStage.STAGE1_UPLOAD, "User started new project from Script Playground")
                st.rerun()
                return
        with col2:
            if st.button("⚙️ Generate Scripts", use_container_width=True):
                state.advance_to(StateStage.STAGE6_GENERATE, "User navigated to script generation from Script Playground")
                st.rerun()
                return
        with col3:
            if st.button("🔧 Optimize Scripts", use_container_width=True):
                state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated to optimization from Script Playground")
                st.rerun()
                return
        return

    # Handle no test cases available
    if not available_test_cases:
        render_no_test_cases_message()
        
        if st.button("📁 Upload Test Cases", use_container_width=True, type="primary"):
            state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to upload from Script Playground")
            st.rerun()
            return
        return

    # Main workflow: template selection, test case selection, and generation
    _handle_main_workflow(state, optimized_scripts, available_test_cases)


def _handle_main_workflow(state, optimized_scripts, available_test_cases):
    """
    Handle the main Stage 10 workflow using UI components.
    
    Args:
        state: StateManager instance
        optimized_scripts: List of available optimized scripts
        available_test_cases: List of available test cases
    """
    # Create template and test case mappings
    template_map = {}
    for script in optimized_scripts:
        display_info = format_template_script_display(script)
        option_text = f"{display_info['title']} - {display_info['timestamp']}"
        template_map[option_text] = script

    test_case_map = {}
    for test_case in available_test_cases:
        option_text = format_test_case_display(test_case)
        test_case_map[option_text] = test_case

    # Render template selection interface
    selected_template, _ = render_template_selection_interface(optimized_scripts, template_map)
    
    if not selected_template:
        return

    # Render test case selection interface
    selected_test_case = render_test_case_selection_interface(available_test_cases, test_case_map)
    
    if not selected_test_case:
        return

    # Render script generation controls
    generation_result = render_script_generation_controls(selected_template, selected_test_case)
    
    if generation_result and len(generation_result) == 4:
        custom_instructions, preserve_structure, include_error_handling, generate_clicked = generation_result
        
        if generate_clicked:
            _generate_script_from_template(
                state, selected_template, selected_test_case,
                custom_instructions, preserve_structure, include_error_handling
            )

    # Handle script execution section
    _display_script_execution_section_if_available(state)

    # Render footer and navigation
    render_stage10_footer()
    
    # Handle workflow navigation
    stage1_clicked, stage8_clicked, stage9_clicked = render_workflow_navigation()
    
    if stage1_clicked:
        debug("User navigated to Stage 1 from Script Playground")
        state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to Stage 1 from Script Playground")
        st.rerun()
    elif stage8_clicked:
        debug("User navigated to Stage 8 from Script Playground")
        state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated to Stage 8 from Script Playground")
        st.rerun()
    elif stage9_clicked:
        debug("User navigated to Stage 9 from Script Playground")
        state.advance_to(StateStage.STAGE9_BROWSE, "User navigated to Stage 9 from Script Playground")
        st.rerun()


def _generate_script_from_template(state, template_script, target_test_case,
                                 custom_instructions, preserve_structure, include_error_handling):
    """
    Generate a new script using the selected template and test case.

    Args:
        state: StateManager instance
        template_script: Selected template script
        target_test_case: Target test case for generation
        custom_instructions: User's custom instructions
        preserve_structure: Whether to preserve template structure
        include_error_handling: Whether to include error handling
    """
    try:
        debug("Starting template-based script generation")

        with st.spinner("🤖 Generating script from template..."):
            # Extract template structure information
            template_structure_info = extract_template_structure_info(template_script.get('content', ''))

            # Generate the prompt
            base_prompt = generate_template_based_script_prompt(
                template_script=template_script,
                target_test_case=target_test_case,
                template_structure_info=template_structure_info,
                website_url=getattr(state, 'website_url', None)
            )

            # Enhance prompt with additional context
            additional_context = {
                'custom_instructions': custom_instructions if custom_instructions else None,
                'preserve_structure': preserve_structure,
                'include_error_handling': include_error_handling
            }

            enhanced_prompt = enhance_template_prompt_with_context(base_prompt, additional_context)

            # Generate script using Google AI
            debug("Calling Google AI for template-based script generation")
            generated_script = generate_llm_response(
                prompt=enhanced_prompt,
                model_name="gemini-2.0-flash",
                api_key=getattr(state, 'google_api_key', None),
                category="template_script_generation",
                context={
                    'template_test_case_id': template_script.get('test_case_id', 'unknown'),
                    'target_test_case_id': target_test_case.get('Test Case ID', 'unknown'),
                    'template_script_id': template_script.get('id', 'unknown'),
                    'generation_type': 'template_based'
                }
            )

            if generated_script and generated_script.strip():
                _handle_successful_generation(state, template_script, target_test_case, generated_script)
            else:
                st.error("❌ Failed to generate script. Please try again.")
                debug("Template-based script generation failed - empty response")

    except Exception as e:
        error_msg = f"Template-based script generation failed: {e}"
        st.error(f"❌ **Generation Error**: {error_msg}")
        debug(f"Template-based script generation error: {e}")


def _handle_successful_generation(state, template_script, target_test_case, generated_script):
    """
    Handle successful script generation with display, storage, and execution using UI components.

    Args:
        state: StateManager instance
        template_script: Template script used
        target_test_case: Target test case
        generated_script: Generated script content
    """
    try:
        debug("Handling successful template-based script generation")

        # Parse the generated script to extract clean Python code from markdown
        debug(f"Raw generated script length: {len(generated_script)} characters")
        parsed_script = clean_llm_response(generated_script, "python")
        debug(f"Parsed script length: {len(parsed_script)} characters")

        # Create filename
        filename = create_template_generation_filename(template_script, target_test_case)

        # Use UI component to render the generation success display
        render_generation_success_display(parsed_script, filename, target_test_case, template_script)

        # Save to script storage
        template_metadata = {
            'generation_type': 'template_based',
            'template_script_id': template_script.get('id'),
            'template_test_case_id': template_script.get('test_case_id'),
            'target_test_case_id': target_test_case.get('Test Case ID'),
            'generation_timestamp': datetime.now().isoformat(),
            'template_based': True,
            'optimization_status': 'template_generated'
        }

        state.add_script_to_history(
            script_content=parsed_script,
            script_type='template_generated',
            step_no=None,
            file_path=filename,
            metadata=template_metadata
        )

        debug(f"Template-based script saved with filename: {filename}")

        # Store generation data in session state for execution section (using parsed script)
        st.session_state['stage10_generated_script'] = {
            'script_content': parsed_script,
            'filename': filename,
            'target_test_case': target_test_case,
            'template_script': template_script,
            'generation_timestamp': datetime.now().isoformat(),
            'raw_generated_content': generated_script  # Keep original for debugging if needed
        }

        debug(f"Stored generated script data in session state for execution: {filename}")

    except Exception as e:
        error_msg = f"Failed to handle successful generation: {e}"
        st.error(f"❌ **Storage Error**: {error_msg}")
        debug(f"Error handling successful generation: {e}")


def _display_script_execution_section_if_available(state):
    """
    Display script execution section using UI components.

    This function coordinates the script execution interface using the refactored
    UI components while maintaining the core business logic.

    Args:
        state: StateManager instance
    """
    try:
        # Check if we have a generated script in session state
        if 'stage10_generated_script' not in st.session_state:
            return

        script_data = st.session_state['stage10_generated_script']
        generated_script = script_data.get('script_content')
        filename = script_data.get('filename')
        target_test_case = script_data.get('target_test_case')

        if not all([generated_script, filename, target_test_case]):
            debug("Incomplete script data in session state, skipping execution section")
            return

        debug(f"Displaying execution section for generated script: {filename}")

        # Render professional section header
        render_script_execution_section_header()

        # Check execution status for status indicators
        execution_key = f"stage10_execution_results_{filename}"
        has_test_results = execution_key in st.session_state
        execution_status = "ready"

        if has_test_results:
            test_results = st.session_state[execution_key]
            execution_status = "passed" if test_results.get('success', False) else "failed"

        # Render script information card
        render_script_info_card(script_data, target_test_case, execution_status)

        # Render execution controls
        _render_execution_controls_with_ui_components(state, script_data, execution_status, filename)

        # Display execution results if available
        if has_test_results:
            test_results = st.session_state[execution_key]
            _display_execution_results_with_ui_components(test_results, target_test_case)

    except Exception as e:
        error_msg = f"Failed to display script execution section: {e}"
        st.error(f"❌ **Execution Display Error**: {error_msg}")
        debug(f"Error displaying script execution section: {e}")


def _render_execution_controls_with_ui_components(state, script_data, execution_status, filename):
    """
    Render execution controls using UI components.

    Args:
        state: StateManager instance
        script_data: Script data from session state
        execution_status: Current execution status
        filename: Script filename
    """
    generated_script = script_data.get('script_content')
    target_test_case = script_data.get('target_test_case')
    verbose_mode_key = f"stage10_verbose_{filename}"
    execution_key = f"stage10_execution_results_{filename}"

    # Render execution controls header
    render_execution_controls_header()

    # Render execution options card
    render_execution_options_card()

    # Render verbose mode checkbox
    verbose_mode = render_verbose_mode_checkbox(verbose_mode_key)

    # Render execution status indicator
    render_execution_status_indicator(execution_status)

    # Define callback functions for button actions
    def execute_callback():
        _execute_generated_script(state, generated_script, filename, target_test_case, verbose_mode)

    def clear_callback():
        # Clear the generated script from session state
        if 'stage10_generated_script' in st.session_state:
            del st.session_state['stage10_generated_script']
        # Clear execution results
        if execution_key in st.session_state:
            del st.session_state[execution_key]
        st.success("✅ Script cleared successfully!")
        st.rerun()

    # Render execution action buttons
    render_execution_action_buttons(execution_status, filename, execute_callback, clear_callback)


def _display_execution_results_with_ui_components(test_results, target_test_case):
    """
    Display execution results using UI components.

    Args:
        test_results: Test execution results dictionary
        target_test_case: Target test case information
    """
    # Render execution results header
    render_execution_results_header()

    # Render execution results summary
    render_execution_results_summary(test_results, target_test_case)

    # Render execution metrics
    render_execution_metrics_header()

    # Parse and display JUnit XML results if available
    xml_results = test_results.get('xml_results')
    if xml_results:
        render_junit_metrics_grid(xml_results)

    # Render execution output section
    verbose_mode = st.session_state.get(f"stage10_verbose_{test_results.get('script_path', '')}", False)
    render_execution_output_section(test_results, verbose_mode)


def _execute_generated_script(state, generated_script, filename, target_test_case, verbose_mode=False):
    """
    Execute the generated script using the same infrastructure as Stage 7/8.

    Args:
        state: StateManager instance
        generated_script: Generated script content
        filename: Script filename
        target_test_case: Target test case information
        verbose_mode: Whether to use verbose execution mode
    """
    try:
        debug(f"Executing generated script: {filename}")

        # Create a temporary file for the script
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
            temp_file.write(generated_script)
            temp_script_path = temp_file.name

        debug(f"Created temporary script file: {temp_script_path}")

        with st.spinner(f"🧪 Executing generated script for {target_test_case.get('Test Case ID', 'Unknown')}..."):
            try:
                # Execute the script using enhanced Stage 10 execution method
                test_results = _execute_script_with_conftest(
                    script_path=temp_script_path,
                    filename=filename,
                    target_test_case=target_test_case,
                    verbose_mode=verbose_mode
                )

                # Store results in session state for persistence
                execution_key = f"stage10_execution_results_{filename}"
                st.session_state[execution_key] = test_results

                debug(f"Script execution completed and results stored: {filename}")

                # Show success/failure message
                if test_results.get('success', False):
                    st.success(f"✅ **Script executed successfully!** Test case {target_test_case.get('Test Case ID', 'Unknown')} passed.")
                else:
                    st.error(f"❌ **Script execution failed.** Check the results below for details.")

                # Trigger rerun to display results
                st.rerun()

            finally:
                # Clean up temporary file
                try:
                    if os.path.exists(temp_script_path):
                        os.unlink(temp_script_path)
                        debug(f"Cleaned up temporary script file: {temp_script_path}")
                except Exception as cleanup_error:
                    debug(f"Failed to clean up temporary script file: {cleanup_error}")

    except Exception as e:
        error_msg = f"Failed to execute generated script: {e}"
        st.error(f"❌ **Execution Error**: {error_msg}")
        debug(f"Script execution error: {e}")


def _execute_script_with_conftest(script_path, filename, target_test_case, verbose_mode=False):
    """
    Execute a generated script with proper conftest.py integration and pytest configuration.

    This function ensures that:
    1. The script runs from the correct working directory (GretahAI_ScriptWeaver)
    2. The conftest.py file is accessible for browser fixture
    3. Required pytest plugins are handled gracefully
    4. Proper error handling for missing dependencies
    5. Temporary script is copied to project directory to ensure proper rootdir detection

    Args:
        script_path: Path to the temporary script file
        filename: Original filename for logging
        target_test_case: Target test case information
        verbose_mode: Whether to use verbose execution mode

    Returns:
        dict: Test execution results
    """
    try:
        debug(f"Executing script with conftest integration: {filename}")

        # Determine the correct working directory (GretahAI_ScriptWeaver root)
        current_dir = os.getcwd()
        scriptweaver_dir = None

        # Check if we're already in GretahAI_ScriptWeaver directory
        if "GretahAI_ScriptWeaver" in current_dir:
            if current_dir.endswith("GretahAI_ScriptWeaver"):
                scriptweaver_dir = current_dir
            else:
                # Navigate to GretahAI_ScriptWeaver directory
                parts = current_dir.split(os.sep)
                try:
                    scriptweaver_index = parts.index("GretahAI_ScriptWeaver")
                    scriptweaver_dir = os.sep.join(parts[:scriptweaver_index + 1])
                except ValueError:
                    scriptweaver_dir = current_dir
        else:
            # Fallback to current directory
            scriptweaver_dir = current_dir

        debug(f"Using working directory: {scriptweaver_dir}")

        # Verify conftest.py exists
        conftest_path = os.path.join(scriptweaver_dir, "conftest.py")
        if not os.path.exists(conftest_path):
            debug(f"Warning: conftest.py not found at {conftest_path}")
            st.warning("⚠️ conftest.py not found - browser fixture may not be available")
        else:
            debug(f"Found conftest.py at {conftest_path}")

        # CRITICAL FIX: Copy temporary script to GretahAI_ScriptWeaver directory
        # This ensures pytest uses the correct rootdir where conftest.py is located
        local_script_name = f"temp_stage10_script_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        local_script_path = os.path.join(scriptweaver_dir, local_script_name)

        debug(f"Copying script from {script_path} to {local_script_path}")
        shutil.copy2(script_path, local_script_path)

        # Use the local script path for execution
        execution_script_path = local_script_path

        # Set environment variables for the test run
        env = os.environ.copy()
        env["HEADLESS"] = "0"  # Always run in visible mode for Stage 10
        env["PYTEST_QUIET_MODE"] = "1" if not verbose_mode else "0"

        # Generate timestamped result file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_xml_path = os.path.join(scriptweaver_dir, f"results_stage10_{timestamp}.xml")

        # Build pytest command with proper configuration
        pytest_command = [
            "pytest",
            execution_script_path,  # Use local script path
            f"--junitxml={result_xml_path}",
            "--tb=short",  # Short traceback format
            "-v" if verbose_mode else "-q",  # Verbose or quiet mode
            "--capture=no",  # Don't capture output for better debugging
            f"--rootdir={scriptweaver_dir}",  # Explicitly set rootdir
        ]

        # Add pytest-order plugin if available, otherwise warn and continue
        try:
            import pytest_order
            debug("pytest-order plugin is available")
        except ImportError:
            debug("pytest-order plugin not available - order decorators will be ignored")
            st.warning("⚠️ pytest-order plugin not installed - test execution order may not be preserved")
            # Add option to ignore unknown markers
            pytest_command.extend(["--disable-warnings", "-p", "no:warnings"])

        debug(f"Executing pytest command: {' '.join(pytest_command)}")
        debug(f"Working directory: {scriptweaver_dir}")
        debug(f"Script path: {execution_script_path}")
        debug(f"Conftest path: {conftest_path}")

        # Execute the test script
        result = subprocess.run(
            pytest_command,
            capture_output=True,
            text=True,
            env=env,
            cwd=scriptweaver_dir  # This is crucial for conftest.py access
        )

        debug(f"Pytest execution completed with return code: {result.returncode}")
        debug(f"Stdout length: {len(result.stdout)} characters")
        debug(f"Stderr length: {len(result.stderr)} characters")

        # Parse JUnit XML results if available
        xml_results = None
        performance_metrics = {}
        artifacts = {}

        if os.path.exists(result_xml_path):
            try:
                from core.junit_parser import parse_junit_xml, format_test_results_for_display
                xml_results = parse_junit_xml(result_xml_path)
                if xml_results:
                    formatted_results = format_test_results_for_display(xml_results)
                    performance_metrics = formatted_results.get("performance_summary", {})

                    # Extract artifacts from test details
                    for test_detail in formatted_results.get("test_details", []):
                        test_artifacts = test_detail.get("artifacts", {})
                        if test_artifacts:
                            artifacts.update(test_artifacts)
                debug(f"Successfully parsed JUnit XML results")
            except Exception as e:
                debug(f"Failed to parse JUnit XML: {e}")
        else:
            debug(f"JUnit XML file not found: {result_xml_path}")

        # Check for screenshots
        screenshots = []
        screenshots_dir = Path(scriptweaver_dir) / "screenshots"
        if screenshots_dir.exists():
            screenshot_files = list(screenshots_dir.glob("*.png"))
            screenshot_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            screenshots = [str(f) for f in screenshot_files[:5]]  # Get up to 5 most recent
            debug(f"Found {len(screenshots)} screenshots")

        # Prepare the results dictionary
        execution_results = {
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode,
            "success": result.returncode == 0,
            "screenshots": screenshots,
            "script_path": script_path,
            "test_context": f"Stage 10 generated script ({filename})",
            "xml_path": result_xml_path if os.path.exists(result_xml_path) else None,
            "xml_results": xml_results,
            "performance_metrics": performance_metrics,
            "artifacts": artifacts,
            "timestamp": timestamp,
            "working_directory": scriptweaver_dir,
            "conftest_available": os.path.exists(conftest_path),
            "local_script_path": local_script_path  # Store for cleanup
        }

        debug(f"Script execution completed successfully")

        # Clean up the temporary local script file
        try:
            if os.path.exists(local_script_path):
                os.unlink(local_script_path)
                debug(f"Cleaned up temporary local script: {local_script_path}")
        except Exception as cleanup_error:
            debug(f"Failed to clean up temporary local script: {cleanup_error}")

        return execution_results

    except Exception as e:
        error_msg = f"Failed to execute script with conftest integration: {e}"
        debug(error_msg)

        # Clean up the temporary local script file if it was created
        try:
            if 'local_script_path' in locals() and os.path.exists(local_script_path):
                os.unlink(local_script_path)
                debug(f"Cleaned up temporary local script after error: {local_script_path}")
        except Exception as cleanup_error:
            debug(f"Failed to clean up temporary local script after error: {cleanup_error}")

        # Return error results
        return {
            "stdout": "",
            "stderr": error_msg,
            "returncode": -1,
            "success": False,
            "screenshots": [],
            "script_path": script_path,
            "test_context": f"Stage 10 generated script ({filename})",
            "xml_path": None,
            "xml_results": None,
            "performance_metrics": {},
            "artifacts": {},
            "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "error": str(e),
            "working_directory": os.getcwd(),
            "conftest_available": False
        }
