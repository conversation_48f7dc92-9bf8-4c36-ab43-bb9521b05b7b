"""
Stage 10 UI Components for GretahAI ScriptWeaver

Minimalist UI components for Script Template Manager with professional enterprise styling.
"""

import streamlit as st
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

# Import core dependencies
from debug_utils import debug

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage10_ui")


def render_empty_playground_message():
    """
    Render the empty playground message.
    """
    st.info("🎮 **No Templates Available**")
    st.markdown("Complete Stage 8 to create optimized script templates.")

    col1, col2, col3 = st.columns(3)
    return col1, col2, col3


def render_no_test_cases_message():
    """
    Render the no test cases available message.
    """
    st.warning("⚠️ **No Test Cases Available**")
    st.markdown("Upload a CSV file with test cases first.")


def render_template_selection_interface(optimized_scripts, template_map):
    """
    Render the template selection interface.

    Args:
        optimized_scripts: List of available optimized scripts
        template_map: Mapping of display options to script objects

    Returns:
        tuple: (selected_template, template_options) or (None, None) if no templates
    """
    with st.expander("🎯 Template Selection", expanded=True):
        if not optimized_scripts:
            st.info("No templates available.")
            return None, None

        # Import template helpers inside function to avoid circular imports
        from core.template_helpers import format_template_script_display

        # Create template options
        template_options = []
        for script in optimized_scripts:
            display_info = format_template_script_display(script)
            option_text = f"{display_info['title']} - {display_info['timestamp']}"
            template_options.append(option_text)

        if not template_options:
            st.info("No template options available.")
            return None, None

        selected_template_option = st.selectbox(
            "Template",
            template_options,
            key="template_selection"
        )

        selected_template = template_map[selected_template_option]

        # Display template details
        _render_template_details_columns(selected_template)

        # Template preview toggle
        if st.checkbox("📄 Preview", key="show_template_preview"):
            template_content = selected_template.get('content', 'No content available')
            st.code(template_content, language='python')

        return selected_template, template_options


def _render_template_details_columns(selected_template):
    """
    Render template details in two-column layout.

    Args:
        selected_template: Selected template script object
    """
    from core.template_helpers import format_template_script_display

    display_info = format_template_script_display(selected_template)

    col1, col2 = st.columns(2)
    with col1:
        st.info(f"""
        **Details:**
        - Test Case: {selected_template.get('test_case_id', 'Unknown')}
        - Created: {display_info['timestamp']}
        - Size: {display_info['size_info']}
        """)

    with col2:
        st.info(f"""
        **Status:**
        - ✅ Optimized
        - Type: {selected_template.get('type', 'Unknown').title()}
        - {display_info['optimization_info']}
        """)


def render_test_case_selection_interface(available_test_cases, test_case_map):
    """
    Render the test case selection interface.

    Args:
        available_test_cases: List of available test cases
        test_case_map: Mapping of display options to test case objects

    Returns:
        selected_test_case or None
    """
    with st.expander("📋 Target Test Case", expanded=True):
        if not available_test_cases:
            st.info("No test cases available.")
            return None

        # Import template helpers inside function to avoid circular imports
        from core.template_helpers import format_test_case_display

        # Create test case options
        test_case_options = []
        for test_case in available_test_cases:
            option_text = format_test_case_display(test_case)
            test_case_options.append(option_text)

        if not test_case_options:
            st.info("No test case options available.")
            return None

        selected_test_case_option = st.selectbox(
            "Test Case",
            test_case_options,
            key="test_case_selection"
        )

        selected_test_case = test_case_map[selected_test_case_option]

        # Display test case details
        _render_test_case_details(selected_test_case)

        return selected_test_case


def _render_test_case_details(selected_test_case):
    """
    Render test case details.

    Args:
        selected_test_case: Selected test case object
    """
    tc_id = selected_test_case.get('Test Case ID', 'Unknown')
    tc_objective = selected_test_case.get('Test Case Objective', 'No objective specified')
    tc_steps = selected_test_case.get('Steps', [])

    st.info(f"""
    **Target:**
    - ID: {tc_id}
    - Objective: {tc_objective}
    - Steps: {len(tc_steps)}
    """)


def render_script_generation_controls(selected_template, selected_test_case):
    """
    Render the script generation controls interface.

    Args:
        selected_template: Selected template script
        selected_test_case: Selected target test case

    Returns:
        tuple: (custom_instructions, preserve_structure, include_error_handling, generate_clicked)
    """
    with st.expander("🤖 Generation", expanded=True):
        # Validate inputs
        from core.template_helpers import validate_template_generation_inputs

        is_valid, error_message = validate_template_generation_inputs(selected_template, selected_test_case)

        if not is_valid:
            st.error(f"❌ {error_message}")
            return None, None, None, False

        # Generation controls
        col1, col2 = st.columns(2)

        with col1:
            custom_instructions = st.text_area(
                "Custom Instructions",
                placeholder="Optional modifications...",
                key="template_custom_instructions"
            )

        with col2:
            st.markdown("**Settings:**")
            preserve_structure = st.checkbox("Preserve Structure", value=True, key="preserve_structure")
            include_error_handling = st.checkbox("Error Handling", value=True, key="include_error_handling")

        # Generate button
        generate_clicked = st.button("🚀 Generate Script", use_container_width=True, type="primary")

        return custom_instructions, preserve_structure, include_error_handling, generate_clicked


def render_script_execution_section_header():
    """
    Render the script execution section header.
    """
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; margin: 2rem 0;">
        <h2 style="color: var(--primary-color); font-weight: 700; margin-bottom: 0.5rem;">
            🧪 Test Script
        </h2>
    </div>
    """, unsafe_allow_html=True)


def render_script_info_card(script_data, target_test_case, execution_status):
    """
    Render script information card with status indicators.

    Args:
        script_data: Script data from session state
        target_test_case: Target test case information
        execution_status: Current execution status (ready, passed, failed)
    """
    filename = script_data.get('filename', 'Unknown')
    tc_id = target_test_case.get('Test Case ID', 'Unknown')
    generation_time = script_data.get('generation_timestamp', 'Unknown')

    # Format timestamp
    if generation_time != 'Unknown':
        try:
            dt = datetime.fromisoformat(generation_time.replace('Z', '+00:00'))
            generation_time = dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            pass

    # Status indicator
    status_map = {
        "ready": ("🔵", "Ready"),
        "passed": ("✅", "Passed"),
        "failed": ("❌", "Failed")
    }

    status_icon, status_text = status_map.get(execution_status, ("🔵", "Ready"))

    # Display status
    if execution_status == "ready":
        st.info(f"{status_icon} {status_text}")
    elif execution_status == "passed":
        st.success(f"{status_icon} {status_text}")
    elif execution_status == "failed":
        st.error(f"{status_icon} {status_text}")

    # Script information
    st.markdown("### 📄 Script Info")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown(f"""
        **File:** `{filename}`
        **Generated:** {generation_time}
        """)

    with col2:
        objective = target_test_case.get('Test Case Objective', 'Not specified')
        truncated_objective = objective[:60] + '...' if len(objective) > 60 else objective
        st.markdown(f"""
        **Test Case:** {tc_id}
        **Objective:** {truncated_objective}
        """)


def render_execution_controls_header():
    """
    Render the execution controls header.
    """
    st.markdown("""
    <div style="margin: 2rem 0 1.5rem 0;">
        <h3 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1.4rem; font-weight: 700;">
            ⚙️ Execution
        </h3>
    </div>
    """, unsafe_allow_html=True)


def render_execution_options_card():
    """
    Render the execution options card.
    """
    st.markdown("""
    <div class="pro-card" style="margin: 1rem 0; padding: 1.5rem;">
        <h4 style="color: var(--primary-color); margin: 0 0 1rem 0; font-size: 1.1rem;">
            🔧 Options
        </h4>
    </div>
    """, unsafe_allow_html=True)


def render_verbose_mode_checkbox(verbose_mode_key):
    """
    Render the verbose mode checkbox.

    Args:
        verbose_mode_key: Key for the verbose mode checkbox

    Returns:
        bool: Verbose mode setting
    """
    return st.checkbox(
        "Verbose Mode",
        value=False,
        key=verbose_mode_key,
        help="Show detailed output"
    )


def render_execution_status_indicator(execution_status):
    """
    Render the execution status indicator.

    Args:
        execution_status: Current execution status
    """
    status_messages = {
        "ready": {
            "icon": "💡",
            "text": "Ready",
            "color": "#2196F3"
        },
        "passed": {
            "icon": "✅",
            "text": "Passed",
            "color": "#4CAF50"
        },
        "failed": {
            "icon": "❌",
            "text": "Failed",
            "color": "#F44336"
        }
    }

    if execution_status in status_messages:
        status_info = status_messages[execution_status]
        st.markdown(f"""
        <div style="
            background: rgba({','.join(str(int(status_info['color'][i:i+2], 16)) for i in (1, 3, 5))}, 0.1);
            border: 1px solid {status_info['color']}40;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            display: flex;
            align-items: center;
            gap: 0.8rem;
        ">
            <span style="font-size: 1.2rem;">{status_info['icon']}</span>
            <span style="color: {status_info['color']}; font-weight: 600;">{status_info['text']}</span>
        </div>
        """, unsafe_allow_html=True)


def render_execution_action_buttons(execution_status, filename, execute_callback, clear_callback):
    """
    Render execution action buttons.

    Args:
        execution_status: Current execution status
        filename: Script filename for button keys
        execute_callback: Callback function for execute button
        clear_callback: Callback function for clear button

    Returns:
        tuple: (execute_clicked, rerun_clicked, clear_clicked)
    """
    execute_clicked = False
    rerun_clicked = False
    clear_clicked = False

    # Button layout
    if execution_status in ["passed", "failed"]:
        # Show all three buttons when script has been executed
        button_col1, button_col2, button_col3 = st.columns([1, 1, 1])

        with button_col1:
            button_type = "primary" if execution_status == "ready" else "secondary"
            execute_clicked = st.button(
                "🚀 Execute",
                use_container_width=True,
                type=button_type,
                key=f"execute_{filename}"
            )

        with button_col2:
            rerun_clicked = st.button(
                "🔄 Re-run",
                use_container_width=True,
                key=f"rerun_{filename}"
            )

        with button_col3:
            clear_clicked = st.button(
                "🗑️",
                use_container_width=True,
                key=f"clear_{filename}",
                type="secondary"
            )
    else:
        # Show only execute and clear buttons for ready state
        button_col1, button_col2 = st.columns([2, 1])

        with button_col1:
            execute_clicked = st.button(
                "🚀 Execute",
                use_container_width=True,
                type="primary",
                key=f"execute_{filename}"
            )

        with button_col2:
            clear_clicked = st.button(
                "🗑️",
                use_container_width=True,
                key=f"clear_{filename}",
                type="secondary"
            )

    # Handle button clicks
    if execute_clicked or rerun_clicked:
        if execute_callback:
            execute_callback()

    if clear_clicked:
        if clear_callback:
            clear_callback()

    return execute_clicked, rerun_clicked, clear_clicked


def render_execution_results_header():
    """
    Render the execution results header.
    """
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; margin: 2rem 0 1.5rem 0; width: 100%;">
        <h2 style="color: var(--primary-color); font-weight: 700; margin-bottom: 0.5rem; font-size: 1.8rem;">
            📊 Results
        </h2>
    </div>
    """, unsafe_allow_html=True)


def render_execution_results_summary(test_results, target_test_case):
    """
    Render the execution results summary with status-based styling.

    Args:
        test_results: Test execution results dictionary
        target_test_case: Target test case information
    """
    # Basic execution status
    success = test_results.get('success', False)
    timestamp = test_results.get('timestamp', 'Unknown')

    # Results summary card with status-based styling - full width layout
    status_color = "#4CAF50" if success else "#F44336"
    status_bg = "rgba(76, 175, 80, 0.1)" if success else "rgba(244, 67, 54, 0.1)"
    status_icon = "✅" if success else "❌"
    status_text = "PASSED" if success else "FAILED"

    # Format timestamp for better display
    formatted_timestamp = timestamp
    if timestamp != 'Unknown':
        try:
            if isinstance(timestamp, str):
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                formatted_timestamp = dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                formatted_timestamp = str(timestamp)
        except:
            formatted_timestamp = str(timestamp)

    st.markdown(f"""
    <div style="
        background: linear-gradient(135deg, {status_bg}, rgba(255,255,255,0.05));
        border: 1px solid {status_color}40;
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        width: 100%;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    ">
        <div style="display: grid; grid-template-columns: auto 1fr auto; gap: 2rem; align-items: center;">
            <div style="
                background: {status_bg};
                color: {status_color};
                padding: 0.8rem 1.8rem;
                border-radius: 30px;
                font-weight: 700;
                font-size: 1.2rem;
                display: flex;
                align-items: center;
                gap: 0.8rem;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            ">
                <span style="font-size: 1.4rem;">{status_icon}</span>
                {status_text}
            </div>
            <div style="text-align: center; color: var(--text-color-light);">
                <div style="font-size: 1rem; opacity: 0.8; margin-bottom: 0.3rem;">Test Case</div>
                <div style="font-weight: 600; font-size: 1.1rem; color: var(--primary-color);">{target_test_case.get('Test Case ID', 'Unknown')}</div>
            </div>
            <div style="text-align: right; color: var(--text-color-light);">
                <div style="font-size: 0.9rem; opacity: 0.8; margin-bottom: 0.3rem;">Completed</div>
                <div style="font-weight: 600; font-size: 1rem;">{formatted_timestamp}</div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)


def render_execution_metrics_header():
    """
    Render the execution metrics header.
    """
    st.markdown("""
    <div style="margin: 2rem 0 1rem 0;">
        <h3 style="color: var(--primary-color); margin-bottom: 0.5rem; font-size: 1.4rem; font-weight: 700;">
            📈 Metrics
        </h3>
    </div>
    """, unsafe_allow_html=True)


def render_junit_metrics_grid(xml_results):
    """
    Render JUnit XML metrics in a responsive grid layout.

    Args:
        xml_results: Parsed JUnit XML results
    """
    if not xml_results or "summary" not in xml_results:
        return

    summary = xml_results["summary"]

    # Display metrics using responsive grid layout for full width
    total_tests = summary.get("total_tests", 0)
    passed_tests = summary.get("passed_tests", 0)
    failed_tests = summary.get("failed_tests", 0)
    duration = summary.get("duration", 0)

    st.markdown(f"""
    <div style="
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin: 1.5rem 0;
        width: 100%;
    ">
        <div style="
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(103, 58, 183, 0.08), rgba(103, 58, 183, 0.03));
            border-radius: 12px;
            border: 1px solid rgba(103, 58, 183, 0.2);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        ">
            <div style="font-size: 2rem; font-weight: bold; color: var(--primary-color); margin-bottom: 0.5rem;">{total_tests}</div>
            <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Total Tests</div>
        </div>
        <div style="
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.08), rgba(76, 175, 80, 0.03));
            border-radius: 12px;
            border: 1px solid rgba(76, 175, 80, 0.2);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        ">
            <div style="font-size: 2rem; font-weight: bold; color: #4CAF50; margin-bottom: 0.5rem;">{passed_tests}</div>
            <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Passed</div>
        </div>
        <div style="
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.08), rgba(244, 67, 54, 0.03));
            border-radius: 12px;
            border: 1px solid rgba(244, 67, 54, 0.2);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        ">
            <div style="font-size: 2rem; font-weight: bold; color: #F44336; margin-bottom: 0.5rem;">{failed_tests}</div>
            <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Failed</div>
        </div>
        <div style="
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.08), rgba(255, 152, 0, 0.03));
            border-radius: 12px;
            border: 1px solid rgba(255, 152, 0, 0.2);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        ">
            <div style="font-size: 2rem; font-weight: bold; color: #FF9800; margin-bottom: 0.5rem;">{duration:.2f}s</div>
            <div style="font-size: 1rem; color: var(--text-color-light); font-weight: 600;">Duration</div>
        </div>
    </div>
    """, unsafe_allow_html=True)


def render_execution_output_section(test_results, verbose_mode=False):
    """
    Render the execution output section with stdout/stderr.

    Args:
        test_results: Test execution results
        verbose_mode: Whether to show verbose output
    """
    stdout = test_results.get('stdout', '')
    stderr = test_results.get('stderr', '')

    if verbose_mode and (stdout or stderr):
        st.markdown("### 📋 Output")

        if stdout:
            with st.expander("📤 stdout", expanded=False):
                st.code(stdout, language='text')

        if stderr:
            with st.expander("⚠️ stderr", expanded=False):
                st.code(stderr, language='text')


def render_stage10_footer():
    """
    Render the Stage 10 footer.
    """
    st.markdown("---")
    st.markdown("### 🎮 Script Playground")

    col1, col2 = st.columns(2)

    with col1:
        st.info("""
        **🔄 Always Accessible**: Available at any time.

        **🎯 Template-Based**: Uses optimized script patterns.
        """)

    with col2:
        st.info("""
        **🤖 AI-Powered**: Google AI adaptation.

        **⚡ Independent**: Doesn't affect workflow.
        """)


def render_workflow_navigation():
    """
    Render the workflow navigation section.

    Returns:
        tuple: (stage1_clicked, stage8_clicked, stage9_clicked)
    """
    with st.expander("🧭 Navigation", expanded=False):
        col1, col2, col3 = st.columns(3)

        stage1_clicked = False
        stage8_clicked = False
        stage9_clicked = False

        with col1:
            stage1_clicked = st.button("📁 Stage 1", use_container_width=True)

        with col2:
            stage8_clicked = st.button("🔧 Stage 8", use_container_width=True)

        with col3:
            stage9_clicked = st.button("📜 Stage 9", use_container_width=True)

        return stage1_clicked, stage8_clicked, stage9_clicked


def render_generation_success_display(parsed_script, filename, target_test_case, template_script):
    """
    Render the successful script generation display.

    Args:
        parsed_script: Clean parsed script content
        filename: Generated filename
        target_test_case: Target test case information
        template_script: Template script used
    """
    # Display success message
    st.success("✅ **Script Generated**")

    # Display parsed script
    st.markdown("### 📄 Script")
    st.code(parsed_script, language='python')

    # Download and copy buttons
    col1, col2 = st.columns(2)
    with col1:
        st.download_button(
            label="📥 Download",
            data=parsed_script,
            file_name=filename,
            mime="text/x-python",
            use_container_width=True
        )

    with col2:
        if st.button("📋 Copy", use_container_width=True):
            st.code(parsed_script)
            st.info("Script displayed for copying")

    # Display generation summary
    st.info(f"""
    **Summary:**
    - Template: {template_script.get('test_case_id', 'Unknown')}
    - Target: {target_test_case.get('Test Case ID', 'Unknown')}
    - Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    - File: {filename}
    """)
